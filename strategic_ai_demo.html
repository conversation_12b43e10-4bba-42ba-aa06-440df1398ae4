<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategic AI Demo - Advanced Battleship AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .grid {
            display: inline-block;
            margin: 10px;
            border: 3px solid #333;
            background: white;
        }
        .grid-row {
            display: flex;
            margin: 0;
            padding: 0;
        }
        .cell {
            width: 25px;
            height: 25px;
            border: 1px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .cell.hit { background-color: #ff4444; color: white; font-weight: bold; }
        .cell.miss { background-color: #4444ff; color: white; font-weight: bold; }
        .cell.ship { background-color: #44ff44; color: #333; font-weight: bold; }
        .cell.forbidden { background-color: #ffcccc; color: #666; font-weight: bold; }
        .cell.strategic { background-color: #ffffcc; color: #333; font-weight: bold; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        .btn-demo { background: #28a745; }
        .btn-demo:hover { background: #1e7e34; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: white;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .log {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            background: #f9f9f9;
        }
        .strategy-explanation {
            background: #e8f4f8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Strategic AI Demo - Advanced Battleship AI</h1>
        <p><strong>This demo showcases advanced AI strategies including the critical "no adjacent ships" rule optimization.</strong></p>

        <div class="demo-section">
            <div class="demo-title">🎯 Demo 1: Strategic Ship Adjacency Rule</div>
            <div class="strategy-explanation">
                <strong>Strategy:</strong> Ships cannot be placed adjacent to each other (including diagonally). 
                When a ship is sunk, the AI should never waste shots on the 8 surrounding cells.
            </div>
            <button class="btn-demo" onclick="demoAdjacencyRule()">Demo Adjacency Rule</button>
            <button onclick="clearDemo()">Clear</button>
            <div id="adjacencyBoard" class="grid"></div>
            <div id="adjacencyLog" class="log"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">🔍 Demo 2: Strategic Ship Size Analysis</div>
            <div class="strategy-explanation">
                <strong>Strategy:</strong> AI analyzes remaining ship sizes and prioritizes cells that can accommodate 
                the largest remaining ships, maximizing strategic value.
            </div>
            <button class="btn-demo" onclick="demoShipSizeAnalysis()">Demo Ship Size Strategy</button>
            <div id="shipSizeBoard" class="grid"></div>
            <div id="shipSizeLog" class="log"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">⚡ Demo 3: Advanced Targeting Algorithms</div>
            <div class="strategy-explanation">
                <strong>Strategy:</strong> When targeting, AI uses strategic ship completion analysis, 
                cluster-based targeting, and Bayesian prediction for optimal moves.
            </div>
            <button class="btn-demo" onclick="demoAdvancedTargeting()">Demo Advanced Targeting</button>
            <div id="targetingBoard" class="grid"></div>
            <div id="targetingLog" class="log"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">🎮 Full Game Demo</div>
            <div class="strategy-explanation">
                <strong>Complete AI Test:</strong> Watch the AI play a full game using all advanced strategies.
            </div>
            <button onclick="startFullDemo()">Start Full Game</button>
            <button onclick="runAIMove()">Single AI Move</button>
            <button onclick="runMultipleAI()">Run 5 AI Moves</button>
            <button onclick="autoCompleteGame()">Auto-Complete Game</button>
            
            <div class="stats">
                <div class="stat-item"><strong>AI Mode:</strong> <span id="mode">hunt</span></div>
                <div class="stat-item"><strong>Queue:</strong> <span id="queue">0</span></div>
                <div class="stat-item"><strong>Hits:</strong> <span id="hits">0</span></div>
                <div class="stat-item"><strong>Shots:</strong> <span id="shots">0</span></div>
                <div class="stat-item"><strong>Hit Rate:</strong> <span id="hitRate">0%</span></div>
                <div class="stat-item"><strong>Ships Sunk:</strong> <span id="shipsSunk">0</span></div>
            </div>

            <div style="display: flex; gap: 20px;">
                <div>
                    <h4>Human Board (AI attacks here)</h4>
                    <div id="humanBoard" class="grid"></div>
                </div>
                <div>
                    <h4>AI Board (Human attacks here)</h4>
                    <div id="aiBoard" class="grid"></div>
                </div>
            </div>

            <div id="gameLog" class="log"></div>
        </div>
    </div>

    <script>
        // Demo logging function
        function logToDemo(containerId, message) {
            const logDiv = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Demo 1: Adjacency Rule
        function demoAdjacencyRule() {
            const board = document.getElementById('adjacencyBoard');
            board.innerHTML = '';
            
            // Create 10x10 grid
            for (let row = 1; row <= 10; row++) {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'grid-row';
                
                for (let col = 1; col <= 10; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    rowDiv.appendChild(cell);
                }
                board.appendChild(rowDiv);
            }
            
            logToDemo('adjacencyLog', 'Demo: Strategic Adjacency Rule');
            
            // Simulate a sunk ship at (5,5) to (5,7) - horizontal 3-cell ship
            const sunkShipCells = [
                {row: 5, col: 5},
                {row: 5, col: 6},
                {row: 5, col: 7}
            ];
            
            // Mark sunk ship
            sunkShipCells.forEach(cell => {
                const cellElement = board.querySelector(`[data-row="${cell.row}"][data-col="${cell.col}"]`);
                cellElement.classList.add('hit');
                cellElement.textContent = 'X';
            });
            
            logToDemo('adjacencyLog', 'Sunk ship placed at (5,5) to (5,7)');
            
            // Mark forbidden adjacent cells (ships can't touch)
            const forbiddenCells = [];
            sunkShipCells.forEach(shipCell => {
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        
                        const adjRow = shipCell.row + dr;
                        const adjCol = shipCell.col + dc;
                        
                        if (adjRow >= 1 && adjRow <= 10 && adjCol >= 1 && adjCol <= 10) {
                            const key = `${adjRow}-${adjCol}`;
                            if (!forbiddenCells.includes(key)) {
                                forbiddenCells.push(key);
                            }
                        }
                    }
                }
            });
            
            // Visual demonstration
            setTimeout(() => {
                forbiddenCells.forEach(cellKey => {
                    const [row, col] = cellKey.split('-').map(Number);
                    const cellElement = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    if (cellElement && !cellElement.classList.contains('hit')) {
                        cellElement.classList.add('forbidden');
                        cellElement.textContent = '✗';
                    }
                });
                
                logToDemo('adjacencyLog', `Marked ${forbiddenCells.length} cells as forbidden (ships can't be adjacent)`);
                logToDemo('adjacencyLog', 'RED cells = sunk ship, PINK cells = forbidden (never target)');
            }, 1000);
        }

        // Demo 2: Ship Size Analysis
        function demoShipSizeAnalysis() {
            const board = document.getElementById('shipSizeBoard');
            board.innerHTML = '';
            
            // Create 10x10 grid
            for (let row = 1; row <= 10; row++) {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'grid-row';
                
                for (let col = 1; col <= 10; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    rowDiv.appendChild(cell);
                }
                board.appendChild(rowDiv);
            }
            
            logToDemo('shipSizeLog', 'Demo: Strategic Ship Size Analysis');
            logToDemo('shipSizeLog', 'Remaining ships: [5, 4, 3] - AI prioritizes cells that can fit size 5');
            
            // Simulate some previous hits and misses
            const hits = [[2,3], [6,8]];
            const misses = [[1,1], [3,3], [7,7], [9,9]];
            
            hits.forEach(([row, col]) => {
                const cell = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.classList.add('hit');
                cell.textContent = 'X';
            });
            
            misses.forEach(([row, col]) => {
                const cell = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.classList.add('miss');
                cell.textContent = '•';
            });
            
            // Highlight strategic cells for size 5 ship
            setTimeout(() => {
                const strategicCells = [
                    [5,3], [5,4], [5,5], [5,6], // Horizontal possibilities
                    [3,5], [4,5], [5,5], [6,5], [7,5] // Vertical possibilities
                ];
                
                strategicCells.forEach(([row, col]) => {
                    const cell = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    if (cell && !cell.classList.contains('hit') && !cell.classList.contains('miss')) {
                        cell.classList.add('strategic');
                        cell.textContent = '★';
                    }
                });
                
                logToDemo('shipSizeLog', 'YELLOW cells = high strategic value for size 5 ship placement');
            }, 1000);
        }

        // Demo 3: Advanced Targeting
        function demoAdvancedTargeting() {
            const board = document.getElementById('targetingBoard');
            board.innerHTML = '';
            
            // Create 10x10 grid
            for (let row = 1; row <= 10; row++) {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'grid-row';
                
                for (let col = 1; col <= 10; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    rowDiv.appendChild(cell);
                }
                board.appendChild(rowDiv);
            }
            
            logToDemo('targetingLog', 'Demo: Advanced Targeting Algorithms');
            
            // Simulate partial ship hits
            const partialHits = [[4,4], [4,5]]; // Horizontal ship partially hit
            
            partialHits.forEach(([row, col]) => {
                const cell = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.classList.add('hit');
                cell.textContent = 'X';
            });
            
            logToDemo('targetingLog', 'Partial ship detected at (4,4)-(4,5)');
            
            // Show strategic completion targets
            setTimeout(() => {
                const completionTargets = [[4,3], [4,6]]; // Logical completion points
                
                completionTargets.forEach(([row, col]) => {
                    const cell = board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    cell.classList.add('strategic');
                    cell.textContent = '🎯';
                });
                
                logToDemo('targetingLog', 'AI identifies optimal completion targets: (4,3) and (4,6)');
                logToDemo('targetingLog', 'Strategy: Complete horizontal ship pattern first');
            }, 1500);
        }

        function clearDemo() {
            document.getElementById('adjacencyBoard').innerHTML = '';
            document.getElementById('adjacencyLog').innerHTML = '';
            document.getElementById('shipSizeBoard').innerHTML = '';
            document.getElementById('shipSizeLog').innerHTML = '';
            document.getElementById('targetingBoard').innerHTML = '';
            document.getElementById('targetingLog').innerHTML = '';
        }

        // Initialize demos
        window.onload = function() {
            logToDemo('adjacencyLog', 'Strategic AI Demo Ready');
            logToDemo('shipSizeLog', 'Ship Size Analysis Demo Ready');
            logToDemo('targetingLog', 'Advanced Targeting Demo Ready');
        };

        // Placeholder functions for full game demo
        function startFullDemo() { alert('Full game demo - integrate with your GameEngine'); }
        function runAIMove() { alert('Single AI move - integrate with your GameEngine'); }
        function runMultipleAI() { alert('Multiple AI moves - integrate with your GameEngine'); }
        function autoCompleteGame() { alert('Auto-complete - integrate with your GameEngine'); }
    </script>
</body>
</html>
