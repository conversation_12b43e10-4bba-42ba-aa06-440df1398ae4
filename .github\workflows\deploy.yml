name: Deploy to GitHub Pages

# Trigger deployment khi push lên main branch
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# Cho phép chạy workflow thủ công
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      pages: write
      id-token: write

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 22
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'
    - run: npm ci
    - run: npm run build

    - name: Setup Pages 
      uses: actions/configure-pages@v4

    - name: Upload artifact 📤
      uses: actions/upload-pages-artifact@v3
      with:
        path: './dist'

    - name: Deploy
      id: deployment
      uses: actions/deploy-pages@v4
