@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  body, * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: -0.01em;
  }

  .font-display {
    font-family: 'Poppins', sans-serif;
  }
}

/* Modern Color Palette */
:root {
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  
  --secondary-50: #fff7ed;
  --secondary-100: #ffedd5;
  --secondary-200: #fed7aa;
  --secondary-300: #fdba74;
  --secondary-400: #fb923c;
  --secondary-500: #f97316;
  --secondary-600: #ea580c;
  --secondary-700: #c2410c;
  --secondary-800: #9a3412;
  --secondary-900: #7c2d12;
  
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  --danger-400: #f87171;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
}

/* Color Classes */
.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-500 { background-color: var(--primary-500); }
.bg-primary-600 { background-color: var(--primary-600); }
.border-primary-200 { border-color: var(--primary-200); }
.border-primary-300 { border-color: var(--primary-300); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }

.bg-secondary-500 { background-color: var(--secondary-500); }
.bg-secondary-600 { background-color: var(--secondary-600); }

.bg-neutral-50 { background-color: var(--neutral-50); }
.bg-neutral-100 { background-color: var(--neutral-100); }
.bg-neutral-700 { background-color: var(--neutral-700); }
.bg-neutral-800 { background-color: var(--neutral-800); }
.text-neutral-600 { color: var(--neutral-600); }
.text-neutral-700 { color: var(--neutral-700); }
.text-neutral-800 { color: var(--neutral-800); }

.bg-success-100 { background-color: rgba(34, 197, 94, 0.1); }
.text-success-600 { color: var(--success-600); }
.text-success-700 { color: var(--success-500); }

.bg-white { background-color: white; }
.text-white { color: white; }

/* Glassmorphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Ship Card Components */
.ship-card-compact {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border: 1px solid var(--primary-200);
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.ship-card-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
  transition: left 0.6s ease;
}

.ship-card-compact:hover::before {
  left: 100%;
}

.ship-card-compact:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(14, 165, 233, 0.25);
  border-color: var(--primary-400);
}

.ship-card-selected {
  background: linear-gradient(135deg, var(--primary-50) 0%, rgba(14, 165, 233, 0.1) 100%);
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(14, 165, 233, 0.1), 0 10px 10px -5px rgba(14, 165, 233, 0.1);
  outline: 2px solid rgba(14, 165, 233, 0.2);
  outline-offset: 2px;
}

.ship-card-disabled {
  background: linear-gradient(135deg, var(--neutral-50) 0%, rgba(34, 197, 94, 0.05) 100%);
  border-color: var(--success-400);
  cursor: not-allowed;
  opacity: 0.75;
}

.ship-card-disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Modern Ship Segments */
.ship-segment-mini {
  width: 1.125rem;
  height: 1.125rem;
  background: linear-gradient(135deg, var(--neutral-600), var(--neutral-700));
  border: 1.5px solid var(--neutral-500);
  border-radius: 0.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.ship-segment-mini::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
}

.ship-card-compact:hover .ship-segment-mini {
  transform: scale(1.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ship-segment-selected {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-color: var(--primary-400);
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

.ship-segment-completed {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  border-color: var(--success-400);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* Modern Control Buttons */
.control-btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.2);
}

.control-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-btn-primary:hover::before {
  opacity: 1;
}

.control-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(14, 165, 233, 0.3);
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

.control-btn-danger {
  background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

.control-btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(239, 68, 68, 0.3);
}

/* Modern Orientation Buttons */
.orientation-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  position: relative;
}

.orientation-btn-active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.2);
  transform: translateY(-1px);
}

.orientation-btn-inactive {
  background: rgba(255, 255, 255, 0.6);
  color: var(--neutral-600);
  border: 1px solid var(--neutral-200);
}

.orientation-btn-inactive:hover {
  background: rgba(255, 255, 255, 0.9);
  color: var(--neutral-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Status Text Colors */
.status-selected {
  color: var(--primary-600);
  font-weight: 600;
}

.status-completed {
  color: var(--success-600);
  font-weight: 600;
}

.status-available {
  color: var(--neutral-500);
  font-weight: 500;
}

/* Responsive */
@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
}
