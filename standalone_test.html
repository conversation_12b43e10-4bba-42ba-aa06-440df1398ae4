<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone AI Test - Battleship</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .grid {
            display: inline-block;
            margin: 10px;
            border: 3px solid #333;
            background: white;
        }
        .grid-row {
            display: flex;
            margin: 0;
            padding: 0;
        }
        .cell {
            width: 25px;
            height: 25px;
            border: 1px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            cursor: pointer;
        }
        .cell.hit { background-color: #ff4444; color: white; font-weight: bold; }
        .cell.miss { background-color: #4444ff; color: white; font-weight: bold; }
        .cell.ship { background-color: #44ff44; color: #333; font-weight: bold; }
        .cell:hover { background-color: #e9ecef; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        .stats {
            margin: 10px 0;
            padding: 10px;
            background: #e8f4f8;
            border-radius: 5px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .stat-item {
            background: white;
            padding: 5px 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .boards {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .board-section {
            flex: 1;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Standalone AI Test - Battleship Game</h1>
        
        <div>
            <button onclick="startNewGame()">New Game</button>
            <button onclick="autoPlaceShips()">Auto Place Ships</button>
            <button onclick="setHardDifficulty()">Set Hard Difficulty</button>
            <button onclick="runAIMove()">Run AI Move</button>
            <button onclick="runMultipleAI()">Run 5 AI Moves</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="stats">
            <div class="stat-item"><strong>AI Mode:</strong> <span id="mode">hunt</span></div>
            <div class="stat-item"><strong>Queue:</strong> <span id="queue">0</span></div>
            <div class="stat-item"><strong>Hits:</strong> <span id="hits">0</span></div>
            <div class="stat-item"><strong>Shots:</strong> <span id="shots">0</span></div>
            <div class="stat-item"><strong>Hit Rate:</strong> <span id="hitRate">0%</span></div>
            <div class="stat-item"><strong>Game Phase:</strong> <span id="gamePhase">early</span></div>
        </div>

        <div class="boards">
            <div class="board-section">
                <h3>Human Board (AI attacks here)</h3>
                <div id="humanBoard" class="grid"></div>
            </div>
            <div class="board-section">
                <h3>AI Board (Human attacks here)</h3>
                <div id="aiBoard" class="grid"></div>
            </div>
        </div>

        <div>
            <h3>AI Decision Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Simple AI Service (simplified version for testing)
        class SimpleAIService {
            constructor(gridSize = 10) {
                this.gridSize = gridSize;
                this.difficulty = 'medium';
                this.mode = 'hunt';
                this.targetQueue = [];
                this.lastHit = null;
                this.hitCells = [];
                this.missedCells = [];
                this.sunkShips = [];
                this.gamePhase = 'early';
                this.currentHuntPattern = 'checkerboard';
                this.adaptiveThreshold = 0.7;
                this.moveHistory = [];
            }

            getNextMove() {
                console.log(`AI getNextMove: difficulty=${this.difficulty} mode=${this.mode} queueLength=${this.targetQueue.length}`);
                
                if (this.difficulty === 'hard') {
                    return this.getAdvancedMove();
                } else {
                    return this.getSmartMove();
                }
            }

            getAdvancedMove() {
                if (this.mode === 'target' && this.targetQueue.length > 0) {
                    const move = this.targetQueue.pop();
                    console.log('AI using target queue:', move);
                    return move;
                }
                return this.getHuntMove();
            }

            getSmartMove() {
                if (this.mode === 'target' && this.targetQueue.length > 0) {
                    return this.targetQueue.pop();
                }
                return this.getHuntMove();
            }

            getHuntMove() {
                // Use checkerboard pattern
                for (let row = 1; row <= this.gridSize; row++) {
                    for (let col = 1; col <= this.gridSize; col++) {
                        if ((row + col) % 2 === 0 && !this.hasBeenTargeted(row, col)) {
                            return { row, col };
                        }
                    }
                }
                
                // Fallback to any available cell
                for (let row = 1; row <= this.gridSize; row++) {
                    for (let col = 1; col <= this.gridSize; col++) {
                        if (!this.hasBeenTargeted(row, col)) {
                            return { row, col };
                        }
                    }
                }
                
                return null;
            }

            hasBeenTargeted(row, col) {
                return this.hitCells.some(cell => cell.row === row && cell.col === col) ||
                       this.missedCells.some(cell => cell.row === row && cell.col === col);
            }

            processResult(row, col, isHit, isShipSunk = false, sunkShipCells = null) {
                console.log(`AI processResult: ${row},${col} hit=${isHit} sunk=${isShipSunk} mode=${this.mode}`);
                
                this.moveHistory.push({ row, col, isHit, timestamp: Date.now() });
                
                if (isHit) {
                    this.hitCells.push({ row, col });
                    this.lastHit = { row, col };
                    this.mode = 'target';
                    
                    if (isShipSunk && sunkShipCells) {
                        this.shipSunk(sunkShipCells);
                    } else {
                        this.addAdjacentTargets(row, col);
                    }
                } else {
                    this.missedCells.push({ row, col });
                    
                    if (this.mode === 'target' && this.targetQueue.length === 0) {
                        this.mode = 'hunt';
                    }
                }
            }

            addAdjacentTargets(row, col) {
                const directions = [
                    { row: row - 1, col },
                    { row: row + 1, col },
                    { row, col: col - 1 },
                    { row, col: col + 1 }
                ];

                directions.forEach(target => {
                    if (this.isValidTarget(target.row, target.col)) {
                        if (!this.targetQueue.some(t => t.row === target.row && t.col === target.col)) {
                            this.targetQueue.push(target);
                        }
                    }
                });
            }

            isValidTarget(row, col) {
                // Basic bounds and targeting check
                if (row < 1 || row > this.gridSize || col < 1 || col > this.gridSize) {
                    return false;
                }

                if (this.hasBeenTargeted(row, col)) {
                    return false;
                }

                // CRITICAL RULE: Ships cannot be placed adjacent to each other
                // Check if target is adjacent to any sunk ship (including diagonally)
                const isAdjacentToSunkShip = this.sunkShips.some(ship =>
                    ship.some(sunkCell => {
                        const rowDiff = Math.abs(sunkCell.row - row);
                        const colDiff = Math.abs(sunkCell.col - col);
                        // Adjacent includes all 8 directions (horizontal, vertical, diagonal)
                        return rowDiff <= 1 && colDiff <= 1 && !(rowDiff === 0 && colDiff === 0);
                    })
                );

                if (isAdjacentToSunkShip) {
                    console.log(`AI: Skipping ${row},${col} - adjacent to sunk ship (ships can't touch)`);
                    return false;
                }

                return true;
            }

            shipSunk(shipCells) {
                console.log('AI: Ship sunk with cells:', shipCells);
                this.sunkShips.push(shipCells);

                // STRATEGIC IMPROVEMENT: Mark all adjacent cells as forbidden
                this.markAdjacentCellsAsForbidden(shipCells);

                // Remove sunk ship cells AND adjacent cells from target queue
                this.targetQueue = this.targetQueue.filter(target => {
                    // Remove if it's the sunk ship cell
                    const isSunkCell = shipCells.some(cell => cell.row === target.row && cell.col === target.col);
                    if (isSunkCell) return false;

                    // Remove if it's adjacent to any sunk ship cell (ships can't be adjacent)
                    const isAdjacentToSunkShip = shipCells.some(cell => {
                        const rowDiff = Math.abs(cell.row - target.row);
                        const colDiff = Math.abs(cell.col - target.col);
                        return rowDiff <= 1 && colDiff <= 1 && !(rowDiff === 0 && colDiff === 0);
                    });

                    return !isAdjacentToSunkShip;
                });

                console.log(`AI: Cleaned up target queue, now has ${this.targetQueue.length} targets`);

                if (this.targetQueue.length === 0) {
                    this.mode = 'hunt';
                }
            }

            markAdjacentCellsAsForbidden(shipCells) {
                const forbiddenCells = new Set();

                shipCells.forEach(shipCell => {
                    // Mark all 8 adjacent cells as forbidden
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            if (dr === 0 && dc === 0) continue; // Skip the ship cell itself

                            const adjRow = shipCell.row + dr;
                            const adjCol = shipCell.col + dc;

                            if (adjRow >= 1 && adjRow <= this.gridSize &&
                                adjCol >= 1 && adjCol <= this.gridSize) {
                                forbiddenCells.add(`${adjRow}-${adjCol}`);
                            }
                        }
                    }
                });

                console.log(`AI: Marked ${forbiddenCells.size} cells as forbidden around sunk ship`);

                // Add these to missed cells to prevent future targeting
                forbiddenCells.forEach(cellKey => {
                    const [row, col] = cellKey.split('-').map(Number);
                    if (!this.hasBeenTargeted(row, col)) {
                        // Mark as "virtually missed" to prevent targeting
                        this.missedCells.push({ row, col, virtual: true });
                    }
                });
            }

            setDifficulty(difficulty) {
                this.difficulty = difficulty;
            }

            generateShipPlacement() {
                const ships = [
                    { name: 'Tàu Sân Bay', size: 5, count: 1 },
                    { name: 'Thiết Giáp Hạm', size: 4, count: 1 },
                    { name: 'Tàu Tuần Dương', size: 3, count: 1 },
                    { name: 'Tàu Ngầm', size: 3, count: 1 },
                    { name: 'Tàu Khu Trục', size: 2, count: 1 }
                ];

                const placedShips = [];
                const occupiedCells = new Set();

                ships.forEach(shipType => {
                    for (let count = 0; count < shipType.count; count++) {
                        let placed = false;
                        let attempts = 0;

                        while (!placed && attempts < 1000) {
                            const row = Math.floor(Math.random() * this.gridSize) + 1;
                            const col = Math.floor(Math.random() * this.gridSize) + 1;
                            const isHorizontal = Math.random() < 0.5;

                            if (this.canPlaceShip(row, col, shipType.size, isHorizontal, occupiedCells)) {
                                const ship = {
                                    row, col, size: shipType.size, isHorizontal, name: shipType.name
                                };
                                
                                placedShips.push(ship);

                                for (let i = 0; i < shipType.size; i++) {
                                    const shipRow = isHorizontal ? row : row + i;
                                    const shipCol = isHorizontal ? col + i : col;
                                    occupiedCells.add(`${shipRow}-${shipCol}`);
                                }

                                placed = true;
                            }
                            attempts++;
                        }
                    }
                });

                return placedShips;
            }

            canPlaceShip(row, col, size, isHorizontal, occupiedCells) {
                if (isHorizontal) {
                    if (col + size - 1 > this.gridSize) return false;
                } else {
                    if (row + size - 1 > this.gridSize) return false;
                }

                for (let i = 0; i < size; i++) {
                    const checkRow = isHorizontal ? row : row + i;
                    const checkCol = isHorizontal ? col + i : col;

                    if (occupiedCells.has(`${checkRow}-${checkCol}`)) return false;

                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            const adjRow = checkRow + dr;
                            const adjCol = checkCol + dc;
                            
                            if (adjRow >= 1 && adjRow <= this.gridSize &&
                                adjCol >= 1 && adjCol <= this.gridSize &&
                                occupiedCells.has(`${adjRow}-${adjCol}`)) {
                                return false;
                            }
                        }
                    }
                }

                return true;
            }

            reset() {
                this.mode = 'hunt';
                this.targetQueue = [];
                this.lastHit = null;
                this.hitCells = [];
                this.missedCells = [];
                this.sunkShips = [];
                this.moveHistory = [];
            }
        }

        // Simple Game Engine (simplified version for testing)
        class SimpleGameEngine {
            constructor(gridSize = 10) {
                this.gridSize = gridSize;
                this.gameMode = 'setup';
                this.currentPlayer = 'human';
                this.isGameStarted = false;
                this.isGameFinished = false;
                this.winner = null;

                this.humanBoard = this.initializeBoard();
                this.aiBoard = this.initializeBoard();
                this.humanShips = [];
                this.aiShips = [];

                this.ai = new SimpleAIService(gridSize);

                this.gameStats = {
                    humanShots: 0,
                    humanHits: 0,
                    aiShots: 0,
                    aiHits: 0,
                    turnCount: 0
                };

                this.shipTypes = [
                    { name: 'Tàu Sân Bay', size: 5, count: 1 },
                    { name: 'Thiết Giáp Hạm', size: 4, count: 1 },
                    { name: 'Tàu Tuần Dương', size: 3, count: 1 },
                    { name: 'Tàu Ngầm', size: 3, count: 1 },
                    { name: 'Tàu Khu Trục', size: 2, count: 1 }
                ];
            }

            initializeBoard() {
                const board = {};
                for (let row = 1; row <= this.gridSize; row++) {
                    board[row] = {};
                    for (let col = 1; col <= this.gridSize; col++) {
                        board[row][col] = {
                            hasShip: false,
                            isHit: false,
                            isMiss: false,
                            shipId: null
                        };
                    }
                }
                return board;
            }

            startGame() {
                this.reset();
                this.gameMode = 'setup';
                return { success: true, message: 'Game khởi tạo thành công. Hãy đặt tàu của bạn!' };
            }

            setHumanShips(ships) {
                this.humanShips = ships;
                this.placeShipsOnBoard(ships, this.humanBoard);

                this.aiShips = this.ai.generateShipPlacement();
                this.placeShipsOnBoard(this.aiShips, this.aiBoard);

                this.gameMode = 'playing';
                this.isGameStarted = true;
                this.currentPlayer = 'human';

                return { success: true, message: 'Game bắt đầu! Lượt của bạn.' };
            }

            placeShipsOnBoard(ships, board) {
                ships.forEach((ship, shipId) => {
                    for (let i = 0; i < ship.size; i++) {
                        const row = ship.isHorizontal ? ship.row : ship.row + i;
                        const col = ship.isHorizontal ? ship.col + i : ship.col;

                        board[row][col].hasShip = true;
                        board[row][col].shipId = shipId;
                    }
                });
            }

            async aiTurn() {
                if (this.gameMode !== 'playing' || this.currentPlayer !== 'ai') {
                    return { success: false, message: 'Không phải lượt của AI!' };
                }

                await new Promise(resolve => setTimeout(resolve, 100));

                const move = this.ai.getNextMove();
                if (!move) {
                    return { success: false, message: 'AI không thể thực hiện nước đi!' };
                }

                this.gameStats.aiShots++;
                const cell = this.humanBoard[move.row][move.col];

                if (cell.hasShip) {
                    cell.isHit = true;
                    this.gameStats.aiHits++;

                    const sunkShip = this.checkShipSunk(move.row, move.col, this.humanBoard, this.humanShips);
                    const sunkShipCells = sunkShip ? this.getShipCells(sunkShip) : null;

                    this.ai.processResult(move.row, move.col, true, !!sunkShip, sunkShipCells);

                    let result = {
                        success: true,
                        isHit: true,
                        isSunk: !!sunkShip,
                        sunkShip: sunkShip,
                        message: sunkShip ? `AI đã đánh chìm ${sunkShip.name} của bạn!` : 'AI trúng mục tiêu!',
                        coordinates: move,
                        nextPlayer: 'ai'
                    };

                    if (this.checkWinCondition(this.humanBoard)) {
                        this.gameMode = 'finished';
                        this.isGameFinished = true;
                        this.winner = 'ai';
                        result.gameOver = true;
                        result.winner = 'ai';
                        result.message = 'AI đã thắng! Hãy thử lại.';
                    }

                    return result;
                } else {
                    cell.isMiss = true;
                    this.ai.processResult(move.row, move.col, false);
                    this.currentPlayer = 'human';

                    return {
                        success: true,
                        isHit: false,
                        message: 'AI bắn trượt! Lượt của bạn.',
                        coordinates: move,
                        nextPlayer: 'human'
                    };
                }
            }

            checkShipSunk(row, col, board, ships) {
                const cell = board[row][col];
                if (!cell.hasShip || cell.shipId === null) return null;

                const ship = ships[cell.shipId];
                if (!ship) return null;

                for (let i = 0; i < ship.size; i++) {
                    const shipRow = ship.isHorizontal ? ship.row : ship.row + i;
                    const shipCol = ship.isHorizontal ? ship.col + i : ship.col;

                    if (!board[shipRow][shipCol].isHit) {
                        return null;
                    }
                }

                return ship;
            }

            getShipCells(ship) {
                const cells = [];
                for (let i = 0; i < ship.size; i++) {
                    const row = ship.isHorizontal ? ship.row : ship.row + i;
                    const col = ship.isHorizontal ? ship.col + i : ship.col;
                    cells.push({ row, col });
                }
                return cells;
            }

            checkWinCondition(board) {
                for (let row = 1; row <= this.gridSize; row++) {
                    for (let col = 1; col <= this.gridSize; col++) {
                        const cell = board[row][col];
                        if (cell.hasShip && !cell.isHit) {
                            return false;
                        }
                    }
                }
                return true;
            }

            getVisibleBoards() {
                const visibleAiBoard = {};
                for (let row = 1; row <= this.gridSize; row++) {
                    visibleAiBoard[row] = {};
                    for (let col = 1; col <= this.gridSize; col++) {
                        const cell = this.aiBoard[row][col];
                        visibleAiBoard[row][col] = {
                            isHit: cell.isHit,
                            isMiss: cell.isMiss,
                            hasShip: cell.isHit && cell.hasShip
                        };
                    }
                }

                return {
                    humanBoard: this.humanBoard,
                    aiBoard: visibleAiBoard
                };
            }

            setAIDifficulty(difficulty) {
                this.ai.setDifficulty(difficulty);
            }

            humanAttack(row, col) {
                if (this.gameMode !== 'playing') {
                    return { success: false, message: 'Game chưa bắt đầu!' };
                }

                if (this.currentPlayer !== 'human') {
                    return { success: false, message: 'Không phải lượt của bạn!' };
                }

                if (this.aiBoard[row][col].isHit || this.aiBoard[row][col].isMiss) {
                    return { success: false, message: 'Ô này đã được bắn rồi!' };
                }

                this.gameStats.humanShots++;
                const cell = this.aiBoard[row][col];

                if (cell.hasShip) {
                    cell.isHit = true;
                    this.gameStats.humanHits++;

                    const sunkShip = this.checkShipSunk(row, col, this.aiBoard, this.aiShips);

                    let result = {
                        success: true,
                        isHit: true,
                        isSunk: !!sunkShip,
                        sunkShip: sunkShip,
                        message: sunkShip ? `Bạn đã đánh chìm ${sunkShip.name}!` : 'Trúng mục tiêu!',
                        coordinates: { row, col }
                    };

                    if (this.checkWinCondition(this.aiBoard)) {
                        this.gameMode = 'finished';
                        this.isGameFinished = true;
                        this.winner = 'human';
                        result.gameOver = true;
                        result.winner = 'human';
                        result.message = 'Chúc mừng! Bạn đã thắng!';
                    }

                    return result;
                } else {
                    cell.isMiss = true;
                    this.currentPlayer = 'ai';

                    return {
                        success: true,
                        isHit: false,
                        message: 'Trượt mục tiêu! Lượt của máy.',
                        coordinates: { row, col }
                    };
                }
            }

            reset() {
                this.gameMode = 'setup';
                this.currentPlayer = 'human';
                this.isGameStarted = false;
                this.isGameFinished = false;
                this.winner = null;

                this.humanBoard = this.initializeBoard();
                this.aiBoard = this.initializeBoard();
                this.humanShips = [];
                this.aiShips = [];

                this.ai.reset();

                this.gameStats = {
                    humanShots: 0,
                    humanHits: 0,
                    aiShots: 0,
                    aiHits: 0,
                    turnCount: 0
                };
            }
        }

        // Make classes globally available
        window.AIService = SimpleAIService;
        window.GameEngine = SimpleGameEngine;

        // Game variables
        let gameEngine = null;
        let isGameReady = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Override console.log to capture AI logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            if (message.includes('AI')) {
                log(message);
            }
        };

        function createBoard(containerId, isHumanBoard = false) {
            const container = document.getElementById(containerId);
            if (!container) {
                log(`Error: Container ${containerId} not found`);
                return;
            }

            container.innerHTML = '';

            for (let row = 1; row <= 10; row++) {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'grid-row';

                for (let col = 1; col <= 10; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.title = `${row},${col}`;

                    if (!isHumanBoard) {
                        cell.onclick = () => humanAttack(row, col);
                    }

                    rowDiv.appendChild(cell);
                }
                container.appendChild(rowDiv);
            }

            log(`Created ${containerId} with ${container.children.length} rows`);
        }

        function updateBoards() {
            if (!gameEngine) return;

            const boards = gameEngine.getVisibleBoards();

            // Update human board
            for (let row = 1; row <= 10; row++) {
                for (let col = 1; col <= 10; col++) {
                    const cell = document.querySelector(`#humanBoard .cell[data-row="${row}"][data-col="${col}"]`);
                    if (!cell) continue;

                    const boardCell = boards.humanBoard[row][col];

                    cell.className = 'cell';
                    cell.textContent = '';

                    if (boardCell.hasShip && !boardCell.isHit) {
                        cell.classList.add('ship');
                        cell.textContent = '■';
                    } else if (boardCell.isHit) {
                        cell.classList.add('hit');
                        cell.textContent = 'X';
                    } else if (boardCell.isMiss) {
                        cell.classList.add('miss');
                        cell.textContent = '•';
                    }
                }
            }

            // Update AI board
            for (let row = 1; row <= 10; row++) {
                for (let col = 1; col <= 10; col++) {
                    const cell = document.querySelector(`#aiBoard .cell[data-row="${row}"][data-col="${col}"]`);
                    if (!cell) continue;

                    const boardCell = boards.aiBoard[row][col];

                    cell.className = 'cell';
                    cell.textContent = '';

                    if (boardCell.isHit) {
                        cell.classList.add('hit');
                        cell.textContent = 'X';
                    } else if (boardCell.isMiss) {
                        cell.classList.add('miss');
                        cell.textContent = '•';
                    }
                }
            }

            updateStats();
        }

        function updateStats() {
            if (!gameEngine) return;

            const ai = gameEngine.ai;
            const stats = gameEngine.gameStats;

            document.getElementById('mode').textContent = ai.mode;
            document.getElementById('queue').textContent = ai.targetQueue.length;
            document.getElementById('hits').textContent = stats.aiHits;
            document.getElementById('shots').textContent = stats.aiShots;

            const hitRate = stats.aiShots > 0 ? Math.round((stats.aiHits / stats.aiShots) * 100) : 0;
            document.getElementById('hitRate').textContent = hitRate + '%';
            document.getElementById('gamePhase').textContent = ai.gamePhase || 'early';
        }

        function startNewGame() {
            try {
                gameEngine = new SimpleGameEngine(10);
                gameEngine.startGame();
                createBoard('humanBoard', true);
                createBoard('aiBoard', false);
                isGameReady = false;
                log('New game started');
                updateBoards();
            } catch (error) {
                log('Error starting game: ' + error.message);
                console.error(error);
            }
        }

        function autoPlaceShips() {
            if (!gameEngine) {
                log('Start a new game first');
                return;
            }

            const ships = [
                { name: 'Tàu Sân Bay', size: 5, row: 1, col: 1, isHorizontal: true },
                { name: 'Thiết Giáp Hạm', size: 4, row: 3, col: 1, isHorizontal: true },
                { name: 'Tàu Tuần Dương', size: 3, row: 5, col: 1, isHorizontal: true },
                { name: 'Tàu Ngầm', size: 3, row: 7, col: 1, isHorizontal: true },
                { name: 'Tàu Khu Trục', size: 2, row: 9, col: 1, isHorizontal: true }
            ];

            const result = gameEngine.setHumanShips(ships);
            if (result.success) {
                isGameReady = true;
                log('Ships placed successfully, game ready');
                updateBoards();
            } else {
                log('Failed to place ships: ' + result.message);
            }
        }

        function setHardDifficulty() {
            if (!gameEngine) {
                log('Start a new game first');
                return;
            }

            gameEngine.setAIDifficulty('hard');
            log('AI difficulty set to HARD - Advanced algorithms enabled');
        }

        async function runAIMove() {
            if (!isGameReady) {
                log('Game not ready - place ships first');
                return;
            }

            gameEngine.currentPlayer = 'ai';
            const result = await gameEngine.aiTurn();
            log(`AI Move: ${result.message}`);
            updateBoards();

            if (result.gameOver) {
                log('Game Over!');
            }
        }

        async function runMultipleAI() {
            if (!isGameReady) {
                log('Game not ready - place ships first');
                return;
            }

            log('=== Running 5 AI moves ===');

            for (let i = 0; i < 5; i++) {
                gameEngine.currentPlayer = 'ai';
                const result = await gameEngine.aiTurn();
                log(`AI Move ${i+1}: ${result.message}`);
                updateBoards();

                if (result.gameOver) {
                    log('Game ended!');
                    break;
                }

                await new Promise(resolve => setTimeout(resolve, 500));
            }

            log('=== Multiple AI moves complete ===');
        }

        function humanAttack(row, col) {
            if (!isGameReady) {
                log('Game not ready - place ships first');
                return;
            }

            const result = gameEngine.humanAttack(row, col);
            log(`Human attack ${row},${col}: ${result.message}`);
            updateBoards();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Initialize when page loads
        window.onload = function() {
            log('Standalone test loaded successfully');
            log('Creating initial boards...');

            // Create empty boards immediately
            createBoard('humanBoard', true);
            createBoard('aiBoard', false);

            log('Ready to start game!');
        };
    </script>
</body>
</html>
