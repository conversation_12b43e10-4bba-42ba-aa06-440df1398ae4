/* Enhanced ShipControls Styling */
.ship-controls-container {
  background: linear-gradient(135deg, #37474F 0%, #455A64 50%, #546E7A 100%);
  border-radius: 16px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 8px 24px rgba(55, 71, 79, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ship-controls-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6F00, #FF8F00, #FFA000, #FF8F00, #FF6F00);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@media (prefers-reduced-motion: reduce) {
  .ship-controls-container::before {
    animation: none;
    background-position: 50% 50%;
  }
}

.ship-controls-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(55, 71, 79, 0.5);
}

.ship-controls-title {
  color: white;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.ships-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.ship-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.ship-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 111, 0, 0.1), transparent);
  transition: left 0.6s ease;
}

.ship-card:hover::before {
  left: 100%;
}

.ship-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 111, 0, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.ship-card.selected {
  background: rgba(255, 111, 0, 0.2);
  border-color: #FF6F00;
  box-shadow: 0 4px 12px rgba(255, 111, 0, 0.3);
}

.ship-card.placed {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  opacity: 0.7;
  cursor: not-allowed;
}

.ship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ship-name {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.ship-size {
  background: rgba(255, 111, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.ship-preview {
  display: flex;
  gap: 2px;
  margin-bottom: 8px;
}

.ship-cell {
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #607D8B, #78909C);
  border: 1px solid #90A4AE;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.ship-card:hover .ship-cell {
  background: linear-gradient(45deg, #FF6F00, #FF8F00);
  border-color: #FF9800;
  transform: scale(1.05);
}

.ship-card.selected .ship-cell {
  background: linear-gradient(45deg, #FF6F00, #FF8F00);
  border-color: #FF9800;
  animation: selectedPulse 2s ease-in-out infinite;
}

.ship-card.placed .ship-cell {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  border-color: #81C784;
}

@keyframes selectedPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.ship-status {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-style: italic;
}

.ship-status.selected {
  color: #FF6F00;
  font-weight: 600;
}

.ship-status.placed {
  color: #4CAF50;
  font-weight: 600;
}

/* Control buttons */
.controls-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.control-button {
  background: linear-gradient(135deg, #FF6F00, #FF8F00);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 111, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.control-button:hover {
  background: linear-gradient(135deg, #FF8F00, #FFA000);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 111, 0, 0.4);
}

.control-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(255, 111, 0, 0.3);
}

.control-button.secondary {
  background: linear-gradient(135deg, #607D8B, #78909C);
  box-shadow: 0 4px 12px rgba(96, 125, 139, 0.3);
}

.control-button.secondary:hover {
  background: linear-gradient(135deg, #78909C, #90A4AE);
  box-shadow: 0 6px 16px rgba(96, 125, 139, 0.4);
}

.control-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Orientation toggle */
.orientation-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.orientation-option {
  flex: 1;
  padding: 8px 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.orientation-option.active {
  background: linear-gradient(135deg, #FF6F00, #FF8F00);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 111, 0, 0.3);
}

.orientation-option:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Progress indicator */
.placement-progress {
  margin: 16px 0;
  text-align: center;
}

.progress-text {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6F00, #FF8F00);
  border-radius: 3px;
  transition: width 0.5s ease;
  box-shadow: 0 0 10px rgba(255, 111, 0, 0.5);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .ship-controls-container {
    margin: 8px;
    padding: 16px;
  }
  
  .ships-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .ship-card {
    padding: 12px;
  }
  
  .control-button {
    padding: 10px 20px;
    font-size: 0.8rem;
  }
  
  .controls-section {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .ship-controls-title {
    font-size: 1.2rem;
    margin-bottom: 16px;
  }
  
  .ship-name {
    font-size: 1rem;
  }
  
  .ship-cell {
    width: 16px;
    height: 16px;
  }
  
  .control-button {
    padding: 8px 16px;
    font-size: 0.75rem;
  }
}
